
name: PPH Bot Release on PR Merge

on:
  pull_request:
    types: [closed]

# Defines two custom environment variables for the workflow. These are used for the Container registry domain, and a name for the Docker image that this workflow builds.
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

# There is a single job in this workflow. It's configured to run on the latest available version of Ubuntu.
jobs:
  pylint-checks:
    name: Pylint Checks
    if: ${{ github.event.pull_request.merged == true }}
    uses: ./.github/workflows/pylint-checks.yml
    secrets: inherit 
  deploy-to-dev:
    name: Deploy application to DEV env
    needs: pylint-checks
    if: ${{ github.event.pull_request.merged == true }}
    uses: ./.github/workflows/release-to-dev.yml
    secrets: inherit 
  create-release-pr:
    name: Create Release Pull Request
    needs: pylint-checks
    if: ${{ github.event.pull_request.merged == true }}
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - # Please follow conventional-commits convention on commits.
        # https://www.conventionalcommits.org/en/v1.0.0/
        name: Create Release
        uses: googleapis/release-please-action@v4
        with:
          release-type: python
          token: ${{ secrets.GITHUB_TOKEN }}
          # target-branch: ${{ github.ref_name }}
          # package-name: "progphil-bot"
  build-and-push-image:
    name: Build docker image for archiving
    needs: create-release-pr
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'main' && contains(github.event.pull_request.head.ref, 'release-')
    runs-on: ubuntu-latest
    environment: prd
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Cleanup config
        run: rm config/dev-config.yml
      - name: Log in to the Container registry
        uses: docker/login-action@65b78e6e13532edd9afa3aa52ac7964289d1a9c1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
      - name: Build and push Docker image
        uses: docker/build-push-action@f2a1d5e99d037542a71f64918e516c093c6f3fc4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
  deploy-to-prd:
    name: Deploy application to PRODUCTION env
    needs: 
      - create-release-pr
      - pylint-checks
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'main' && contains(github.event.pull_request.head.ref, 'release-')
    uses: ./.github/workflows/release-to-prd.yml
    secrets: inherit 
