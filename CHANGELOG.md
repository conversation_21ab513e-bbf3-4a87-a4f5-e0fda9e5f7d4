# Changelog

## [1.3.1](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.3.0...v1.3.1) (2024-12-02)


### Bug Fixes

* Remove reference to dev-config on main.py ([#265](https://github.com/ProgrammingPhilippines/progphil-bot/issues/265)) ([d22e844](https://github.com/ProgrammingPhilippines/progphil-bot/commit/d22e8445e5a7f4f9929df9b95d8cafffd8473486))

## [1.3.0](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.2.0...v1.3.0) (2024-12-01)


### Features

* Thread Showcase ([#260](https://github.com/ProgrammingPhilippines/progphil-bot/issues/260)) ([9d98acb](https://github.com/ProgrammingPhilippines/progphil-bot/commit/9d98acb1bf1828785c0809824f4de64a98651f08))

## [1.2.0](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.1.1...v1.2.0) (2024-09-01)


### Features

* Improvements on Forum Assist [#235](https://github.com/ProgrammingPhilippines/progphil-bot/issues/235) ([ee05a76](https://github.com/ProgrammingPhilippines/progphil-bot/commit/ee05a76b1872197370c12e69e227efec127e087e))


### Bug Fixes

* Disable pph-solved command on non-staff ([1c5e5ec](https://github.com/ProgrammingPhilippines/progphil-bot/commit/1c5e5ecdfce7a9208f3dfde00ad9acd29beac1b4))
* prod-issue [#243](https://github.com/ProgrammingPhilippines/progphil-bot/issues/243) ([953a3c6](https://github.com/ProgrammingPhilippines/progphil-bot/commit/953a3c6e203e8849959522205f54581855d7712f))

## [1.1.1](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.1.0...v1.1.1) (2024-07-30)


### Bug Fixes

* add default value to port ([#225](https://github.com/ProgrammingPhilippines/progphil-bot/issues/225)) ([cbebe91](https://github.com/ProgrammingPhilippines/progphil-bot/commit/cbebe9193b9096cebb66538338787a673f95eeee))

### Chores

* codebase cleanup ([#237](https://github.com/ProgrammingPhilippines/progphil-bot/pull/234)) ([6a748e1](https://github.com/ProgrammingPhilippines/progphil-bot/commit/6a748e1c3f1d9c78cf50a95b4523d7c5462d3ae8))
* remove pph-rphoto command  ([#240](https://github.com/ProgrammingPhilippines/progphil-bot/pull/240)) ([9d328db](https://github.com/ProgrammingPhilippines/progphil-bot/commit/9d328dbba1c2b832b6c4149351cf45fca3f511a0))

## [1.1.0](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.0.1...v1.1.0) (2024-03-17)


### Features

* add forum auto closing ([#135](https://github.com/ProgrammingPhilippines/progphil-bot/issues/135)) ([ce5eff1](https://github.com/ProgrammingPhilippines/progphil-bot/commit/ce5eff15dda5db9e6ba8d846ed7a58c7f71798a5))
* ignore pinned threads from getting archived ([#175](https://github.com/ProgrammingPhilippines/progphil-bot/issues/175)) ([2dee62c](https://github.com/ProgrammingPhilippines/progphil-bot/commit/2dee62c173265517022edc452e1e6822be641791))
* update currency formatting ([#161](https://github.com/ProgrammingPhilippines/progphil-bot/issues/161)) ([0ae542a](https://github.com/ProgrammingPhilippines/progphil-bot/commit/0ae542ae790e95f007709af4ae15051b2ab48944))


### Bug Fixes

* add missing config value ([#112](https://github.com/ProgrammingPhilippines/progphil-bot/issues/112)) ([44a2672](https://github.com/ProgrammingPhilippines/progphil-bot/commit/44a26729fc439c676f140232ed25855a577125c1))
* add missing env variable ([#186](https://github.com/ProgrammingPhilippines/progphil-bot/issues/186)) ([a0738aa](https://github.com/ProgrammingPhilippines/progphil-bot/commit/a0738aa0f786eb68fe735da0228c3719cb863800))
* added try catch block to user dm ([#150](https://github.com/ProgrammingPhilippines/progphil-bot/issues/150)) ([42e0d1e](https://github.com/ProgrammingPhilippines/progphil-bot/commit/42e0d1eae8d8bbd9431dcbe5c9a41ade1f55d1c8))
* fetch threads if it's not in cache ([#209](https://github.com/ProgrammingPhilippines/progphil-bot/issues/209)) ([e201181](https://github.com/ProgrammingPhilippines/progphil-bot/commit/e201181e9aeaa9aa068b6e1a7823e842ccb38bca))
* fix matching conditions ([#191](https://github.com/ProgrammingPhilippines/progphil-bot/issues/191)) ([2c918e8](https://github.com/ProgrammingPhilippines/progphil-bot/commit/2c918e8466e07e2a53de594e7de3311f8ba18a33))
* fixed _format_description ([#193](https://github.com/ProgrammingPhilippines/progphil-bot/issues/193)) ([8abb164](https://github.com/ProgrammingPhilippines/progphil-bot/commit/8abb164ac104e49a0369bee79e7f0b1989796643))
* fixed error on defining slash words ([#114](https://github.com/ProgrammingPhilippines/progphil-bot/issues/114)) ([73035aa](https://github.com/ProgrammingPhilippines/progphil-bot/commit/73035aa7728c547a148c7645aad1f9c60045ef40))
* fixed forum cleanup checks ([#137](https://github.com/ProgrammingPhilippines/progphil-bot/issues/137)) ([120d3e1](https://github.com/ProgrammingPhilippines/progphil-bot/commit/120d3e1cf8ca08d358817639848bf867168e742e))
* fixed quote errors and invalid amount inputs ([#144](https://github.com/ProgrammingPhilippines/progphil-bot/issues/144)) ([1386560](https://github.com/ProgrammingPhilippines/progphil-bot/commit/138656049fb3421ee14002cbee0a1c8cf2bf5d0b))
* fixed response ([#177](https://github.com/ProgrammingPhilippines/progphil-bot/issues/177)) ([22058ad](https://github.com/ProgrammingPhilippines/progphil-bot/commit/22058adf82bdbdfa814629bb60b905d08ad16aae))
* fixed solve button by adding retries ([#202](https://github.com/ProgrammingPhilippines/progphil-bot/issues/202)) ([281a914](https://github.com/ProgrammingPhilippines/progphil-bot/commit/281a91462300c6139c77e04e19fef39130df22d0))
* reverted old formatting to fix locale issues ([#173](https://github.com/ProgrammingPhilippines/progphil-bot/issues/173)) ([9e27853](https://github.com/ProgrammingPhilippines/progphil-bot/commit/9e2785349e2d724015510419a2544aa9083fe617))
* truncate title ([#199](https://github.com/ProgrammingPhilippines/progphil-bot/issues/199)) ([e50790c](https://github.com/ProgrammingPhilippines/progphil-bot/commit/e50790c3035770312019af2770b6c9f0be18f706))

## [1.1.0](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.0.1...v1.1.0) (2024-03-15)


### Features

* add forum auto closing ([#135](https://github.com/ProgrammingPhilippines/progphil-bot/issues/135)) ([ce5eff1](https://github.com/ProgrammingPhilippines/progphil-bot/commit/ce5eff15dda5db9e6ba8d846ed7a58c7f71798a5))
* ignore pinned threads from getting archived ([#175](https://github.com/ProgrammingPhilippines/progphil-bot/issues/175)) ([2dee62c](https://github.com/ProgrammingPhilippines/progphil-bot/commit/2dee62c173265517022edc452e1e6822be641791))
* update currency formatting ([#161](https://github.com/ProgrammingPhilippines/progphil-bot/issues/161)) ([0ae542a](https://github.com/ProgrammingPhilippines/progphil-bot/commit/0ae542ae790e95f007709af4ae15051b2ab48944))


### Bug Fixes

* add missing config value ([#112](https://github.com/ProgrammingPhilippines/progphil-bot/issues/112)) ([44a2672](https://github.com/ProgrammingPhilippines/progphil-bot/commit/44a26729fc439c676f140232ed25855a577125c1))
* add missing env variable ([#186](https://github.com/ProgrammingPhilippines/progphil-bot/issues/186)) ([a0738aa](https://github.com/ProgrammingPhilippines/progphil-bot/commit/a0738aa0f786eb68fe735da0228c3719cb863800))
* added try catch block to user dm ([#150](https://github.com/ProgrammingPhilippines/progphil-bot/issues/150)) ([42e0d1e](https://github.com/ProgrammingPhilippines/progphil-bot/commit/42e0d1eae8d8bbd9431dcbe5c9a41ade1f55d1c8))
* fetch threads if it's not in cache ([#209](https://github.com/ProgrammingPhilippines/progphil-bot/issues/209)) ([e201181](https://github.com/ProgrammingPhilippines/progphil-bot/commit/e201181e9aeaa9aa068b6e1a7823e842ccb38bca))
* fix matching conditions ([#191](https://github.com/ProgrammingPhilippines/progphil-bot/issues/191)) ([2c918e8](https://github.com/ProgrammingPhilippines/progphil-bot/commit/2c918e8466e07e2a53de594e7de3311f8ba18a33))
* fixed _format_description ([#193](https://github.com/ProgrammingPhilippines/progphil-bot/issues/193)) ([8abb164](https://github.com/ProgrammingPhilippines/progphil-bot/commit/8abb164ac104e49a0369bee79e7f0b1989796643))
* fixed error on defining slash words ([#114](https://github.com/ProgrammingPhilippines/progphil-bot/issues/114)) ([73035aa](https://github.com/ProgrammingPhilippines/progphil-bot/commit/73035aa7728c547a148c7645aad1f9c60045ef40))
* fixed forum cleanup checks ([#137](https://github.com/ProgrammingPhilippines/progphil-bot/issues/137)) ([120d3e1](https://github.com/ProgrammingPhilippines/progphil-bot/commit/120d3e1cf8ca08d358817639848bf867168e742e))
* fixed quote errors and invalid amount inputs ([#144](https://github.com/ProgrammingPhilippines/progphil-bot/issues/144)) ([1386560](https://github.com/ProgrammingPhilippines/progphil-bot/commit/138656049fb3421ee14002cbee0a1c8cf2bf5d0b))
* fixed response ([#177](https://github.com/ProgrammingPhilippines/progphil-bot/issues/177)) ([22058ad](https://github.com/ProgrammingPhilippines/progphil-bot/commit/22058adf82bdbdfa814629bb60b905d08ad16aae))
* fixed solve button by adding retries ([#202](https://github.com/ProgrammingPhilippines/progphil-bot/issues/202)) ([281a914](https://github.com/ProgrammingPhilippines/progphil-bot/commit/281a91462300c6139c77e04e19fef39130df22d0))
* reverted old formatting to fix locale issues ([#173](https://github.com/ProgrammingPhilippines/progphil-bot/issues/173)) ([9e27853](https://github.com/ProgrammingPhilippines/progphil-bot/commit/9e2785349e2d724015510419a2544aa9083fe617))
* truncate title ([#199](https://github.com/ProgrammingPhilippines/progphil-bot/issues/199)) ([e50790c](https://github.com/ProgrammingPhilippines/progphil-bot/commit/e50790c3035770312019af2770b6c9f0be18f706))

## [1.0.1](https://github.com/ProgrammingPhilippines/progphil-bot/compare/v1.0.0...v1.0.1) (2023-03-07)


### Features

* 70 trivia ([#84](https://github.com/ProgrammingPhilippines/progphil-bot/issues/84)) ([5396cf5](https://github.com/ProgrammingPhilippines/progphil-bot/commit/5396cf53f355eb96f3c9562840c040595971aca7))


### Bug Fixes

* fixed announcement view channel mentions ([#81](https://github.com/ProgrammingPhilippines/progphil-bot/issues/81)) ([4da2c93](https://github.com/ProgrammingPhilippines/progphil-bot/commit/4da2c93260a243557f2528abe85c2a255d643bcf))

## 1.0.0 (2023-02-06)


### Bug Fixes

* Added Auto release actions using release-please-actions ([#64](https://github.com/ProgrammingPhilippines/progphil-bot/issues/64)) ([0cfe17e](https://github.com/ProgrammingPhilippines/progphil-bot/commit/0cfe17e37e9f7132ebd10fda8cfb24457a373f2f))
* changed dir to pph-prod/progphil-bot based in server. ([#65](https://github.com/ProgrammingPhilippines/progphil-bot/issues/65)) ([6c3d9b7](https://github.com/ProgrammingPhilippines/progphil-bot/commit/6c3d9b78be12d3cc1fa253643d682b6313bfade7))
* fix wether typo ([#61](https://github.com/ProgrammingPhilippines/progphil-bot/issues/61)) ([891cebb](https://github.com/ProgrammingPhilippines/progphil-bot/commit/891cebb305c6ac24d15919c8da49f478890c49b1))
* fixed ssh private key variable name ([#67](https://github.com/ProgrammingPhilippines/progphil-bot/issues/67)) ([9bc20b2](https://github.com/ProgrammingPhilippines/progphil-bot/commit/9bc20b21b82ee0639f460c0d3e836f4a08ee5cb8))
