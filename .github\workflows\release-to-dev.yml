
name: PPH Bot Release to DEV

on:
  workflow_call:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy-to-dev:
    name: Deploy application to DEV env
    runs-on: ubuntu-latest
    environment: dev
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Load DEV config
        run: |
          mv config/dev-config.yml config/config.yml
          rm .gitignore
      - name: Install Railway
        run: npm i -g @railway/cli
      - name: Deploy
        run: |
          ls -al
          railway up -s ${{ vars.RAILWAY_SERVICE }}
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}