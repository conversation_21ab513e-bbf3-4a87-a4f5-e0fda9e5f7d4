[tool.poetry]
name = "progphilbot"
version = "1.3.1"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src" },
    { include = "bot", from = "src" },
    { include = "cogs", from = "src" },
    { include = "*", from = "src/bot" },
]

[tool.poetry.dependencies]
python = "^3.10.14"
discord-py = "^2.4.0"
python-dotenv = "^1.0.1"
pyyaml = "^6.0"
flake8 = "^7.1.0"
asyncpg = "^0.29.0"
requests = "^2.28.2"
yoyo-migrations = "^8.2.0"
psycopg2 = "^2.9.5"
cloudscraper = "^1.2.69"
bs4 = "^0.0.2"
psycopg2-binary = "^2.9.5"
cryptography = "^43.0.1"
python-levenshtein = "^0.25.1"
setuptools = "^70.1.0"
pydantic = "^2.8.0"
python-dateutil = "^2.9.0"

[tool.poetry.scripts]
progphil = "main:run"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
