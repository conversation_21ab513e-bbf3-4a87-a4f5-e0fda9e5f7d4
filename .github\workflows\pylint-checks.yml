# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: <PERSON><PERSON><PERSON> Checks

on:
  push:
    branches-ignore:
      - main
  workflow_call:
  workflow_dispatch:

jobs:
  pylint-checks:
    name: Pyl<PERSON> Checks
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10"]
        poetry-version: ["1.3.2"]

    steps:
    - uses: actions/checkout@v4 
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Setup Poetry
      uses: abatilo/actions-poetry@v2
      with:
          poetry-version: ${{ matrix.poetry-version }}
    - name: Install Poetry Dependencies
      run: |
        poetry add pylint
        poetry install
    - name: Run Pylint Checks
      run: |
        poetry run pylint --errors-only ./src
